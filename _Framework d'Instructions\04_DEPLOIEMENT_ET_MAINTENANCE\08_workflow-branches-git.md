# 🌿 Workflow Git - Gestion des Branches

## 🎯 Problème Identifié

**Incohérence entre branches locales et déploiement :**
- **Local** : Travail sur `master`
- **Netlify** : Déploiement depuis `main`
- **Résultat** : Les commits ne déclenchent pas de déploiement automatique

## 🔧 Solution Mise en Place

### Structure des Branches
```
Local:  master (développement principal)
Remote: master (sauvegarde)
        main   (déploiement Netlify)
```

### Workflow de Déploiement

#### 1. **Développement Normal**
```bash
# Travail habituel sur master
git add .
git commit -m "Votre message"
git push origin master
```

#### 2. **Déploiement vers Netlify**
```bash
# Synchronisation manuelle
git push origin master:main
```

#### 3. **Déploiement Automatisé (Recommandé)**
```powershell
# Utiliser le script de déploiement
.\scripts\deploy.ps1 "Message de commit"
```

## 📜 Scripts Disponibles

### `scripts/deploy.ps1`
**Usage :** `.\scripts\deploy.ps1 "Message de commit"`

**Actions :**
1. ✅ Vérification de la branche (doit être master)
2. 📁 Ajout des fichiers modifiés
3. 💾 Commit avec le message fourni
4. 📤 Push vers origin/master
5. 🔄 Synchronisation vers main (Netlify)

### `scripts/sync-branches.ps1`
**Usage :** `.\scripts\sync-branches.ps1`

**Actions :**
1. ✅ Vérification de l'état du repository
2. 📤 Push de master vers origin/master
3. 🔄 Synchronisation master -> main

## 🚀 Commandes Rapides

### Déploiement Complet
```powershell
.\scripts\deploy.ps1 "Fix: Correction authentification Google"
```

### Synchronisation Seule
```powershell
.\scripts\sync-branches.ps1
```

### Vérification de l'État
```bash
git status
git branch -a
```

## 🔍 Vérification du Déploiement

### 1. **GitHub**
- Vérifier que les commits sont sur `main`
- URL : https://github.com/cisco-03/FloraSynth

### 2. **Netlify**
- Vérifier le déploiement automatique
- URL : https://app.netlify.com

### 3. **Site en Production**
- Tester les fonctionnalités
- URL : https://votre-site.netlify.app

## ⚠️ Points d'Attention

### Erreurs Courantes
1. **"Not on master branch"** → `git checkout master`
2. **"Uncommitted changes"** → Commiter avant de déployer
3. **"Push failed"** → Vérifier la connexion réseau

### Bonnes Pratiques
- ✅ Toujours travailler sur `master`
- ✅ Utiliser les scripts pour déployer
- ✅ Vérifier le déploiement après push
- ✅ Tester en local avant de déployer

## 🔄 Workflow Quotidien

```powershell
# 1. Développement
# ... modifications de code ...

# 2. Test local
npm run dev

# 3. Déploiement
.\scripts\deploy.ps1 "Description des changements"

# 4. Vérification
# Attendre 2-3 minutes et vérifier le site
```

## 📝 Notes Importantes

- **Netlify** écoute uniquement la branche `main`
- **Les scripts** automatisent la synchronisation
- **Le déploiement** prend 2-3 minutes après le push
- **Toujours vérifier** que le déploiement s'est bien passé
