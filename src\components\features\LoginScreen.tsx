
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { signInWithGoogle } from '@/services/api';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';

const LoginScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleGoogleSignIn = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await signInWithGoogle();

      if (!result?.success && result?.error) {
        setError(result.error);
      }
      // Si la connexion réussit, l'AuthContext gèrera automatiquement la redirection
    } catch (err) {
      setError("Une erreur inattendue s'est produite. Veuillez réessayer.");
      console.error("Erreur lors de la connexion:", err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#100f1c] p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md text-center"
      >
        <div className="inline-block p-4 bg-gradient-to-r from-[#d385f5] to-[#a364f7] rounded-full mb-6">
          <LeafIcon className="w-16 h-16 text-white" />
        </div>
        <h1 className="text-5xl font-bold text-white mb-2">FloraSynth</h1>
        <p className="text-lg text-[#E0E0E0] mb-8">Votre Assistant IA Personnel pour le Soin des Plantes</p>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm"
          >
            {error}
          </motion.div>
        )}

        <Button
          onClick={handleGoogleSignIn}
          className="w-full max-w-xs mx-auto"
          disabled={isLoading}
        >
          {isLoading ? 'Connexion en cours...' : 'Se connecter avec Google'}
        </Button>
      </motion.div>
    </div>
  );
};

export default LoginScreen;
